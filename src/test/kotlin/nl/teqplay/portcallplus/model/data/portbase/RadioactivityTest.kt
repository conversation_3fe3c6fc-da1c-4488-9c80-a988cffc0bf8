package nl.teqplay.portcallplus.model.data.portbase

import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers.`is`
import org.hamcrest.Matchers.nullValue
import org.junit.jupiter.api.Test

class RadioactivityTest {

    @Test
    fun `should create Radioactivity with all properties`() {
        val radionuclide = Radionuclide("Uranium-235")
        val radioactivity = Radioactivity(
            radionuclide = radionuclide,
            packageCategory = "Category I",
            identification = "UN2977",
            level = 1000000000L, // Large Long value to test overflow prevention
            transportIndex = "0.1",
            criticalSafetyIndex = 5,
            licenseNumber = "LIC-12345",
            remarks = "Handle with care",
        )

        assertThat(radioactivity.radionuclide, `is`(radionuclide))
        assertThat(radioactivity.packageCategory, `is`("Category I"))
        assertThat(radioactivity.identification, `is`("UN2977"))
        assertThat(radioactivity.level, `is`(1000000000L))
        assertThat(radioactivity.transportIndex, `is`("0.1"))
        assertThat(radioactivity.criticalSafetyIndex, `is`(5))
        assertThat(radioactivity.licenseNumber, `is`("LIC-12345"))
        assertThat(radioactivity.remarks, `is`("Handle with care"))
    }

    @Test
    fun `should create Radioactivity with null properties`() {
        val radioactivity = Radioactivity(
            radionuclide = null,
            packageCategory = null,
            identification = null,
            level = null,
            transportIndex = null,
            criticalSafetyIndex = null,
            licenseNumber = null,
            remarks = null,
        )

        assertThat(radioactivity.radionuclide, `is`(nullValue()))
        assertThat(radioactivity.packageCategory, `is`(nullValue()))
        assertThat(radioactivity.identification, `is`(nullValue()))
        assertThat(radioactivity.level, `is`(nullValue()))
        assertThat(radioactivity.transportIndex, `is`(nullValue()))
        assertThat(radioactivity.criticalSafetyIndex, `is`(nullValue()))
        assertThat(radioactivity.licenseNumber, `is`(nullValue()))
        assertThat(radioactivity.remarks, `is`(nullValue()))
    }

    @Test
    fun `should handle large Long values for level property to prevent overflow`() {
        val maxLongValue = Long.MAX_VALUE
        val radioactivity = Radioactivity(
            radionuclide = Radionuclide("Plutonium-239"),
            packageCategory = "Category III",
            identification = "UN2978",
            level = maxLongValue,
            transportIndex = "10.0",
            criticalSafetyIndex = 100,
            licenseNumber = "LIC-99999",
            remarks = "Maximum level test",
        )

        assertThat(radioactivity.level, `is`(maxLongValue))
        assertThat(radioactivity.radionuclide?.name, `is`("Plutonium-239"))
    }

    @Test
    fun `should create Radioactivity with mixed null and non-null properties`() {
        val radionuclide = Radionuclide("Cesium-137")
        val radioactivity = Radioactivity(
            radionuclide = radionuclide,
            packageCategory = "Category II",
            identification = null,
            level = 500000L,
            transportIndex = null,
            criticalSafetyIndex = 10,
            licenseNumber = null,
            remarks = "Partial data test",
        )

        assertThat(radioactivity.radionuclide, `is`(radionuclide))
        assertThat(radioactivity.packageCategory, `is`("Category II"))
        assertThat(radioactivity.identification, `is`(nullValue()))
        assertThat(radioactivity.level, `is`(500000L))
        assertThat(radioactivity.transportIndex, `is`(nullValue()))
        assertThat(radioactivity.criticalSafetyIndex, `is`(10))
        assertThat(radioactivity.licenseNumber, `is`(nullValue()))
        assertThat(radioactivity.remarks, `is`("Partial data test"))
    }

    @Test
    fun `should verify data class equality and copy functionality`() {
        val radionuclide = Radionuclide("Cobalt-60")
        val radioactivity1 = Radioactivity(
            radionuclide = radionuclide,
            packageCategory = "Category I",
            identification = "UN2919",
            level = 100000L,
            transportIndex = "0.5",
            criticalSafetyIndex = 2,
            licenseNumber = "LIC-54321",
            remarks = "Test material",
        )

        val radioactivity2 = radioactivity1.copy()
        val radioactivity3 = radioactivity1.copy(level = 200000L)

        assertThat(radioactivity1, `is`(radioactivity2))
        assertThat(radioactivity1 == radioactivity3, `is`(false))
        assertThat(radioactivity3.level, `is`(200000L))
        assertThat(radioactivity3.radionuclide, `is`(radionuclide))
    }
}
